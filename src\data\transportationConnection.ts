import { Tile } from '../api/model/tile'


export class TransportationConnection {
    objectId?: number
    npcId?: number
    widgetId?: number

    tile: Tile
    destination: Tile
    otherRequirement?: () => boolean
}


export const TransportationSystemNodes: Record<string, TransportationConnection> = {
    // Example transportation connection - replace with actual coordinates when ready
    QuetzalToTalTeklan: {
        objectId: 12345, // Replace with actual object ID
        tile: new Tile(1000, 1000, 0), // Replace with actual starting tile
        destination: new Tile(2000, 2000, 0) // Replace with actual destination tile
    }
}