import { WebPath<PERSON>inder } from '../api/core/webPathFinder'
import { TileType } from '../api/model/tile'
import { WebNode, WebNodeType } from '../api/model/webNode'
import { Time } from '../api/utils/time'
import { log } from '../api/utils/utils'
import { Boat } from './boat'
import { Teleport } from './teleport'
import { TransportationConnection, TransportationSystemNodes } from './transportationConnection'

export class WebData {
    static nodes: WebNode[]

    static init() {
        const jsonNodes: WebNode[] = require('./WebData.json')
        const nodesMap: Map<number, WebNode> = new Map()

        this.nodes = jsonNodes.map((plain) => this.plainToObject(plain))
        this.nodes.forEach((node) => nodesMap.set(node.id, node))
        this.nodes.forEach((node) => node.setDestinationNodes(nodesMap))

        log(`Loaded ${this.nodes.length} web-walking nodes.`)

        const start = Time.now()
        this.addTeleportNodes()
        this.addBoatNodes()
        this.addTransportationConnectionNodes()
        log(`Added teleport, boat, and transportation connection nodes in ${Time.now() - start}ms.`)
    }

    static plainToObject(plain: WebNode): any {
        const objectNode = new WebNode()
        objectNode.dialogues = plain.dialogues
        objectNode.destinationNodeIds = plain.destinationNodeIds
        objectNode.id = plain.id
        objectNode.type = plain.type
        objectNode.objectName = plain.objectName
        objectNode.npcName = plain.npcName
        objectNode.objectId = plain.objectId
        objectNode.action = plain.action
        objectNode.climbUpAction = plain.climbUpAction
        objectNode.climbDownAction = plain.climbDownAction
        objectNode.teleport = plain.teleport
        objectNode.x = plain.x
        objectNode.y = plain.y
        objectNode.z = plain.z
        objectNode.tileType = TileType.WORLD
        return objectNode
    }

    static addTeleportNodes() {
        let id = 0
        for (const teleport of Teleport.allTeleports) {
            const teleportNode = new WebNode(teleport.destination)
            teleportNode.type = 'INVENTORY_TELEPORT'
            teleportNode.x = teleport.destination.x
            teleportNode.y = teleport.destination.y
            teleportNode.z = teleport.destination.z
            teleportNode.id = ++id
            teleportNode.teleport = teleport
            teleportNode.tileType = TileType.WORLD

            const closest = WebPathFinder.getClosestNode(teleportNode)
            if (closest.distance(teleport.destination) < 15) {
                teleportNode.addDestination(closest)
                this.nodes.push(teleportNode)
            }
        }
    }

    static addBoatNodes() {
        let id = 10_000
        for (const boat of Boat.allBoats) {
            const boatNode = new WebNode(boat.tile)
            boatNode.type = 'BOAT'
            boatNode.x = boat.tile.x
            boatNode.y = boat.tile.y
            boatNode.z = boat.tile.z
            boatNode.id = ++id
            boatNode.boat = boat
            boatNode.tileType = TileType.WORLD
            boatNode.checkRequirements = boat.otherRequirement

            const initialNode = WebPathFinder.getClosestNodeOfType(boat.tile, WebNodeType.REGULAR)
            if (initialNode.distance(boat.tile) < 15) {
                initialNode.addDestination(boatNode)
            }

            const destinationNode = WebPathFinder.getClosestNodeOfType(boat.destination, WebNodeType.REGULAR)
            if (destinationNode.distance(boat.destination) < 15) {
                boatNode.addDestination(destinationNode)
                this.nodes.push(boatNode)
            }

            // log(boat.name, ' initial: ', initialNode?.id, ' destination: ', destinationNode?.id)
        }
    }

    static addTransportationConnectionNodes() {
        let id = 20_000
        for (const [name, transportationConnection] of Object.entries(TransportationSystemNodes)) {
            // Skip incomplete connections
            if (!transportationConnection.tile || !transportationConnection.destination) {
                continue
            }

            const transportationNode = new WebNode(transportationConnection.tile)
            transportationNode.type = 'TRANSPORTATION_CONNECTION'
            transportationNode.x = transportationConnection.tile.x
            transportationNode.y = transportationConnection.tile.y
            transportationNode.z = transportationConnection.tile.z
            transportationNode.id = ++id
            transportationNode.transportationConnection = transportationConnection
            transportationNode.tileType = TileType.WORLD
            transportationNode.checkRequirements = transportationConnection.otherRequirement

            const initialNode = WebPathFinder.getClosestNodeOfType(transportationConnection.tile, WebNodeType.REGULAR)
            if (initialNode.distance(transportationConnection.tile) < 15) {
                initialNode.addDestination(transportationNode)
            }

            const destinationNode = WebPathFinder.getClosestNodeOfType(transportationConnection.destination, WebNodeType.REGULAR)
            if (destinationNode.distance(transportationConnection.destination) < 15) {
                transportationNode.addDestination(destinationNode)
                this.nodes.push(transportationNode)
            }

            log(`Added transportation connection: ${name}`)
        }
    }
}
